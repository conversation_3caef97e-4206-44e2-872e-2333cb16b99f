# PDF预览器 - 跨域问题完整解决方案

这是一个功能完善的PDF预览器，内置了多种跨域问题解决方案。

## 🚀 快速开始

### 方法1：本地文件上传（推荐）
1. 打开 `pdf.html`
2. 点击"选择PDF文件"
3. 选择本地PDF文件进行预览

### 方法2：URL预览（自动处理跨域）
1. 打开 `pdf.html?url=PDF文件的URL`
2. 系统会自动尝试多种方式加载PDF
3. 如果遇到跨域问题，会显示解决方案

## 🔧 跨域解决方案

### 1. 自动代理服务（已内置）
系统会自动尝试以下代理服务：
- api.allorigins.win
- thingproxy.freeboard.io
- api.codetabs.com
- cors.bridged.cc
- cors-anywhere.herokuapp.com

### 2. 本地代理服务器（推荐）
```bash
# 方法1：使用批处理文件（Windows）
双击运行 "启动代理服务器.bat"

# 方法2：手动启动
node cors-proxy-server.js
```

启动后访问：`http://localhost:3000/proxy?url=PDF文件URL`

### 3. 浏览器扩展（临时使用）
**Chrome：**
- CORS Unblock
- Disable Web Security
- CORS Toggle

**Firefox：**
- CORS Everywhere
- Disable CORS

⚠️ **注意：** 使用完毕后请及时禁用扩展

### 4. 服务器端配置（生产环境）
如果您控制PDF文件所在的服务器，可以配置CORS头：

**Apache (.htaccess):**
```apache
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type"
```

**Nginx:**
```nginx
location / {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type";
}
```

## 📁 文件说明

- `pdf.html` - 主要的PDF预览器文件
- `cors-proxy-server.js` - 本地代理服务器
- `启动代理服务器.bat` - Windows快速启动脚本
- `跨域解决方案说明.md` - 详细的跨域问题说明
- `jquery-3.6.0.min.js` - jQuery库
- `pdf.min.js` - PDF.js库
- `pdf.worker.min.js` - PDF.js工作线程

## 🎯 使用场景

### 开发环境
1. **本地开发：** 使用本地代理服务器
2. **测试不同PDF：** 使用内置的代理服务
3. **调试跨域问题：** 查看浏览器控制台日志

### 生产环境
1. **自有服务器：** 配置CORS头
2. **第三方PDF：** 使用后端API代理
3. **用户上传：** 使用文件上传功能

## 🛠️ 故障排除

### 常见问题

**Q: 所有代理服务都失败了怎么办？**
A: 
1. 尝试本地代理服务器
2. 使用浏览器扩展
3. 下载PDF文件后本地上传

**Q: 本地代理服务器启动失败？**
A:
1. 确保已安装Node.js
2. 检查端口3000是否被占用
3. 尝试修改代理服务器中的端口号

**Q: PDF加载很慢？**
A:
1. 检查网络连接
2. 尝试不同的代理服务
3. 考虑下载到本地后上传

## 🔒 安全建议

1. **生产环境：** 不要使用 `*` 通配符，指定具体域名
2. **敏感文件：** 不要通过第三方代理服务加载
3. **浏览器扩展：** 仅在开发时使用，用完即禁用
4. **本地代理：** 仅在本地网络中使用

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台的错误信息
2. 参考 `跨域解决方案说明.md` 文档
3. 尝试不同的解决方案
4. 检查PDF文件URL是否有效

## 🎉 功能特性

- ✅ 智能跨域检测和处理
- ✅ 多种代理服务自动切换
- ✅ 本地文件上传支持
- ✅ 响应式设计，支持移动端
- ✅ 缩放、翻页等完整功能
- ✅ 详细的错误提示和解决方案
- ✅ URL参数支持
- ✅ 触摸手势支持

---

**版本：** 2.0 with Enhanced CORS Solutions  
**更新时间：** 2025-08-26  
**兼容性：** 现代浏览器（Chrome, Firefox, Safari, Edge）
