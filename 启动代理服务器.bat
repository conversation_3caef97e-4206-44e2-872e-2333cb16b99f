@echo off
chcp 65001 >nul
title PDF预览器本地代理服务器

echo.
echo ========================================
echo    PDF预览器本地代理服务器
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js
    echo.
    echo 请先安装Node.js：
    echo 1. 访问 https://nodejs.org
    echo 2. 下载并安装LTS版本
    echo 3. 重新运行此脚本
    echo.
    echo 按任意键打开Node.js官网...
    pause >nul
    start https://nodejs.org
    exit /b 1
)

echo ✅ Node.js已安装
node --version

echo.
echo 🚀 正在启动PDF代理服务器...
echo.
echo 📍 代理服务器: http://localhost:3000
echo 📖 使用说明: http://localhost:3000
echo 🔧 代理格式: http://localhost:3000/proxy?url=PDF文件URL
echo.
echo 💡 使用提示：
echo    1. 保持此窗口打开以维持服务运行
echo    2. 在PDF预览器中输入PDF文件URL
echo    3. 系统会自动通过本地代理加载
echo    4. 按 Ctrl+C 停止服务器
echo.
echo 🌐 现在可以打开PDF预览器: pdf.html
echo.
echo ========================================
echo.

:: 启动Node.js服务器
node cors-proxy-server.js

echo.
echo 🛑 代理服务器已停止
pause
