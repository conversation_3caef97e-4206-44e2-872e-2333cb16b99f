# PDF预览器 - 本地代理使用说明

## 🎯 核心特点

这个PDF预览器现在**只使用本地代理服务器**来解决跨域问题，不再依赖任何第三方代理服务。

## 🚀 快速开始

### 方法1：本地文件上传（推荐）
1. 直接打开 `pdf.html`
2. 点击"选择PDF文件"按钮
3. 选择本地PDF文件进行预览

### 方法2：URL预览（需要本地代理）
1. **启动代理：** 双击运行 `启动代理服务器.bat`
2. **打开预览器：** 访问 `pdf.html?url=PDF文件的URL`
3. **自动加载：** 系统会自动通过本地代理加载PDF

## 🔧 本地代理服务器详解

### 启动方式

**Windows用户（最简单）：**
```
双击运行：启动代理服务器.bat
```

**命令行启动：**
```bash
node cors-proxy-server.js
```

### 服务器信息
- **运行地址：** http://localhost:3000
- **代理格式：** http://localhost:3000/proxy?url=目标PDF的URL
- **状态检查：** http://localhost:3000

### 工作原理
1. 用户输入PDF的URL
2. 系统检测本地代理服务器是否运行
3. 如果运行正常，通过本地代理获取PDF文件
4. 如果未运行，显示启动指导

## ✅ 本地代理的优势

### 安全性
- ✅ 数据完全在本地处理，不经过第三方
- ✅ 可以完全控制代理逻辑
- ✅ 支持访问日志和监控

### 可靠性
- ✅ 不依赖外部服务，避免服务中断
- ✅ 响应速度快，无网络延迟
- ✅ 可以自定义超时和重试逻辑

### 隐私保护
- ✅ PDF文件不会经过第三方服务器
- ✅ 访问记录只保存在本地
- ✅ 完全控制数据流向

## 🛠️ 故障排除

### 代理服务器启动失败
**问题：** 双击bat文件没有反应或报错
**解决：**
1. 确保已安装Node.js（https://nodejs.org）
2. 检查端口3000是否被占用
3. 右键以管理员身份运行bat文件

### PDF加载失败
**问题：** 显示"需要启动本地代理服务器"
**解决：**
1. 确认代理服务器正在运行
2. 访问 http://localhost:3000 检查状态
3. 重新启动代理服务器

### Node.js未安装
**问题：** 提示"未检测到Node.js"
**解决：**
1. 访问 https://nodejs.org
2. 下载并安装LTS版本
3. 重新运行启动脚本

## 📋 使用流程

### 完整使用步骤
1. **准备环境：** 确保安装了Node.js
2. **启动代理：** 双击 `启动代理服务器.bat`
3. **确认运行：** 看到"🚀 CORS代理服务器已启动"消息
4. **打开预览器：** 在浏览器中打开 `pdf.html`
5. **输入URL：** 直接输入PDF文件的URL，或使用URL参数
6. **自动加载：** 系统会自动通过本地代理加载PDF

### URL参数使用
```
# 直接在URL中指定PDF文件
pdf.html?url=https://example.com/document.pdf

# 系统会自动转换为代理地址
http://localhost:3000/proxy?url=https://example.com/document.pdf
```

## 🔒 安全建议

### 本地使用
- ✅ 仅在本地网络中使用
- ✅ 不要将代理服务器暴露到公网
- ✅ 使用完毕后关闭代理服务器

### 监控访问
- ✅ 查看代理服务器的控制台日志
- ✅ 了解哪些PDF文件被访问
- ✅ 监控是否有异常请求

## 📞 技术支持

### 获取帮助
1. **查看日志：** 代理服务器控制台输出
2. **检查状态：** 访问 http://localhost:3000
3. **浏览器调试：** F12开发者工具查看网络请求
4. **备用方案：** 使用本地文件上传功能

### 常用调试命令
```bash
# 检查Node.js版本
node --version

# 检查端口占用（Windows）
netstat -ano | findstr :3000

# 手动启动代理（查看详细日志）
node cors-proxy-server.js
```

---

**版本：** 3.0 Local Proxy Only  
**更新：** 2025-08-26  
**特点：** 专用本地代理，安全可靠
