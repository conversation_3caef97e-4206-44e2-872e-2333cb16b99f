@echo off
chcp 65001 >nul
title PDF跨域代理服务器

echo.
echo ========================================
echo    PDF跨域代理服务器启动脚本
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js
    echo.
    echo 请先安装Node.js：
    echo 1. 访问 https://nodejs.org
    echo 2. 下载并安装最新版本
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js已安装
node --version

echo.
echo 🚀 正在启动代理服务器...
echo.
echo 📍 服务器地址: http://localhost:3000
echo 🔧 代理格式: http://localhost:3000/proxy?url=PDF文件URL
echo 📖 使用说明: http://localhost:3000
echo.
echo 💡 提示：
echo    - 按 Ctrl+C 停止服务器
echo    - 保持此窗口打开以维持服务运行
echo    - 可以在浏览器中访问 http://localhost:3000 查看详细说明
echo.
echo ========================================
echo.

:: 启动Node.js服务器
node cors-proxy-server.js

echo.
echo 服务器已停止
pause
