<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF预览器测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 PDF预览器测试页面</h1>
    
    <div class="test-section">
        <h2>📋 测试清单</h2>
        <div id="proxyStatus" class="status warning">
            🔄 正在检测本地代理服务器状态...
        </div>
        
        <h3>1. 本地代理服务器测试</h3>
        <button onclick="testProxyServer()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            检测代理服务器
        </button>
        
        <h3>2. PDF预览器功能测试</h3>
        <a href="pdf.html" class="test-link" target="_blank">打开PDF预览器</a>
        <a href="pdf.html?url=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" class="test-link" target="_blank">测试URL参数</a>
        
        <h3>3. 代理服务器管理</h3>
        <a href="http://localhost:3000" class="test-link" target="_blank">查看代理状态</a>
        <button onclick="openProxyBat()" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            启动代理服务器
        </button>
    </div>
    
    <div class="test-section">
        <h2>🔧 使用说明</h2>
        
        <h3>步骤1：启动本地代理服务器</h3>
        <div class="code">
            # Windows用户（推荐）
            双击运行：启动代理服务器.bat
            
            # 或者手动启动
            node cors-proxy-server.js
        </div>
        
        <h3>步骤2：测试PDF预览</h3>
        <div class="code">
            # 方法1：本地文件上传
            打开 pdf.html → 选择PDF文件
            
            # 方法2：URL预览
            pdf.html?url=PDF文件的URL
        </div>
        
        <h3>步骤3：验证功能</h3>
        <ul>
            <li>✅ 代理服务器正常运行</li>
            <li>✅ PDF文件能够正常加载</li>
            <li>✅ 缩放、翻页功能正常</li>
            <li>✅ 错误提示清晰明确</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="testResults">
            <p>点击上方按钮开始测试...</p>
        </div>
    </div>

    <script>
        // 检测代理服务器状态
        async function testProxyServer() {
            const statusDiv = document.getElementById('proxyStatus');
            const resultsDiv = document.getElementById('testResults');
            
            statusDiv.innerHTML = '🔄 正在检测代理服务器...';
            statusDiv.className = 'status warning';
            
            try {
                const response = await fetch('http://localhost:3000/', {
                    method: 'HEAD',
                    signal: AbortSignal.timeout(3000)
                });
                
                if (response.ok) {
                    statusDiv.innerHTML = '✅ 本地代理服务器运行正常！';
                    statusDiv.className = 'status success';
                    
                    resultsDiv.innerHTML = `
                        <h3>✅ 测试通过</h3>
                        <p><strong>代理服务器状态：</strong> 运行正常</p>
                        <p><strong>服务器地址：</strong> http://localhost:3000</p>
                        <p><strong>响应时间：</strong> ${Date.now() - startTime}ms</p>
                        <p><strong>建议：</strong> 现在可以正常使用PDF预览器的URL功能</p>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.innerHTML = '❌ 本地代理服务器未运行';
                statusDiv.className = 'status error';
                
                resultsDiv.innerHTML = `
                    <h3>❌ 测试失败</h3>
                    <p><strong>错误信息：</strong> ${error.message}</p>
                    <p><strong>解决方案：</strong></p>
                    <ol>
                        <li>双击运行 "启动代理服务器.bat"</li>
                        <li>或者手动运行：node cors-proxy-server.js</li>
                        <li>确保已安装Node.js</li>
                        <li>检查端口3000是否被占用</li>
                    </ol>
                    <p><strong>备用方案：</strong> 使用本地文件上传功能</p>
                `;
            }
        }
        
        // 尝试打开启动脚本
        function openProxyBat() {
            alert('请手动双击运行项目目录中的 "启动代理服务器.bat" 文件');
        }
        
        // 页面加载时自动检测
        let startTime = Date.now();
        window.onload = function() {
            setTimeout(testProxyServer, 1000);
        };
    </script>
</body>
</html>
