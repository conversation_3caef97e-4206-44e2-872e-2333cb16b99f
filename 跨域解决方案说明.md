# PDF预览器跨域问题解决方案

## 什么是跨域问题？

跨域问题是由浏览器的**同源策略**（Same-Origin Policy）引起的安全限制。当网页尝试从不同的域名、端口或协议加载资源时，浏览器会阻止这种请求，以保护用户安全。

**同源的定义：**
- 相同的协议（http/https）
- 相同的域名
- 相同的端口

## 您的PDF预览器中的跨域解决方案

### 1. 自动降级策略 ⭐
您的代码已经实现了智能的多层降级策略：

```
直接加载 → 代理服务 → 用户选择解决方案
```

### 2. 多个代理服务
代码中配置了多个代理服务，按优先级尝试：
- `api.allorigins.win` - 快速稳定
- `thingproxy.freeboard.io` - 备用选择
- `api.codetabs.com` - 第三选择
- `cors.bridged.cc` - 备用服务
- `cors-anywhere.herokuapp.com` - 经典代理

### 3. 预检测功能 🆕
新增的预检测功能可以快速判断URL是否支持跨域：
- 使用 `HEAD` 请求快速检测
- 3秒超时，避免长时间等待
- 根据检测结果选择最佳加载策略

## 完整的跨域解决方案

### 方案1：使用本地文件上传 ✅ 推荐
**优点：** 100%可靠，无跨域问题
**步骤：**
1. 下载PDF文件到本地
2. 使用文件上传功能
3. 直接在浏览器中预览

### 方案2：本地代理服务器 ✅ 开发推荐
**优点：** 完全控制，安全可靠
**使用方法：**
```bash
# 启动代理服务器
node cors-proxy-server.js

# 访问代理地址
http://localhost:3000/proxy?url=PDF文件URL
```

### 方案3：浏览器扩展 ⚠️ 临时使用
**Chrome扩展：**
- CORS Unblock
- Disable Web Security
- CORS Toggle

**Firefox扩展：**
- CORS Everywhere
- Disable CORS

**注意：** 使用后请及时禁用，避免安全风险

### 方案4：服务器端配置 🔧 最佳长期方案

#### Apache (.htaccess)
```apache
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type"
```

#### Nginx
```nginx
location / {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type";
}
```

#### Node.js (Express)
```javascript
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type');
    next();
});
```

#### PHP
```php
<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
?>
```

### 方案5：第三方代理服务 🌐 备用方案
您的代码已经集成了多个第三方代理服务，会自动尝试。

## 开发环境快速解决方案

### 使用本地HTTP服务器
```bash
# Python 3 (推荐)
python -m http.server 8000 --bind 0.0.0.0

# Node.js
npm install -g http-server
http-server -p 8000 --cors

# PHP
php -S localhost:8000
```

### Chrome开发者模式
```bash
# 启动Chrome并禁用安全检查（仅开发使用）
chrome.exe --user-data-dir="C:/temp/chrome-dev" --disable-web-security --disable-features=VizDisplayCompositor
```

## 生产环境推荐方案

### 1. 服务器端CORS配置 ⭐ 最佳
- 在PDF文件服务器上配置CORS头
- 允许特定域名或所有域名访问
- 最安全、最可靠的解决方案

### 2. 反向代理
```nginx
# Nginx反向代理配置
location /pdf-proxy/ {
    proxy_pass https://external-pdf-server.com/;
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
}
```

### 3. 后端API代理
创建后端API接口，由服务器端获取PDF文件并返回给前端。

## 故障排除

### 常见错误信息
1. **"Access to fetch at ... has been blocked by CORS policy"**
   - 典型的跨域错误
   - 使用代理服务或配置CORS头

2. **"Failed to fetch"**
   - 可能是网络问题或服务器不可达
   - 检查URL是否正确

3. **"TypeError: Failed to construct 'URL'"**
   - URL格式错误
   - 检查URL是否包含协议（http://或https://）

### 调试技巧
1. 打开浏览器开发者工具
2. 查看Network标签页的请求状态
3. 查看Console标签页的错误信息
4. 使用代码中的console.log输出进行调试

## 安全注意事项

1. **生产环境不要使用 `*` 通配符**
   ```javascript
   // 不安全
   res.header('Access-Control-Allow-Origin', '*');
   
   // 安全
   res.header('Access-Control-Allow-Origin', 'https://trusted-domain.com');
   ```

2. **谨慎使用浏览器扩展**
   - 仅在开发测试时使用
   - 使用完毕后立即禁用
   - 不要在处理敏感数据时使用

3. **第三方代理服务风险**
   - 数据会经过第三方服务器
   - 不要代理敏感或私密文件
   - 考虑使用自建代理服务

## 总结

您的PDF预览器已经实现了非常完善的跨域解决方案，包括：
- ✅ 智能降级策略
- ✅ 多代理服务支持
- ✅ 用户友好的错误提示
- ✅ 多种备用方案
- 🆕 预检测功能
- 🆕 本地代理服务器
- 🆕 浏览器扩展推荐

对于大多数使用场景，现有的解决方案已经足够。如果仍然遇到问题，建议优先使用本地文件上传或本地代理服务器方案。
