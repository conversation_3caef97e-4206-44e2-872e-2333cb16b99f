/**
 * 简单的CORS代理服务器
 * 用于解决PDF预览器的跨域问题
 * 
 * 使用方法：
 * 1. 安装Node.js
 * 2. 运行: node cors-proxy-server.js
 * 3. 访问: http://localhost:3000/proxy?url=PDF文件URL
 */

const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3000;

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // 处理预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // 解析请求URL
    const parsedUrl = url.parse(req.url, true);
    
    // 根路径 - 显示使用说明
    if (parsedUrl.pathname === '/') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>CORS代理服务器</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .code { background: #f4f4f4; padding: 10px; border-radius: 5px; font-family: monospace; }
                    .success { color: #28a745; }
                    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🔧 CORS代理服务器</h1>
                    <p class="success">✅ 服务器运行正常 (端口: ${PORT})</p>
                    
                    <h2>使用方法：</h2>
                    <div class="code">
                        http://localhost:${PORT}/proxy?url=PDF文件的完整URL
                    </div>
                    
                    <h2>示例：</h2>
                    <div class="code">
                        http://localhost:${PORT}/proxy?url=https://example.com/document.pdf
                    </div>
                    
                    <h2>在PDF预览器中使用：</h2>
                    <p>将此代理服务器的URL作为PDF文件地址输入到预览器中。</p>
                    
                    <div class="warning">
                        <strong>⚠️ 安全提醒：</strong><br>
                        此代理服务器仅用于开发和测试目的。<br>
                        不要在生产环境中使用，也不要代理敏感内容。
                    </div>
                    
                    <h2>支持的功能：</h2>
                    <ul>
                        <li>✅ 自动添加CORS头</li>
                        <li>✅ 支持HTTP和HTTPS</li>
                        <li>✅ 保持原始内容类型</li>
                        <li>✅ 错误处理和日志</li>
                    </ul>
                </div>
            </body>
            </html>
        `);
        return;
    }
    
    // 代理请求处理
    if (parsedUrl.pathname === '/proxy') {
        const targetUrl = parsedUrl.query.url;
        
        if (!targetUrl) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: '缺少url参数' }));
            return;
        }
        
        console.log(`[${new Date().toISOString()}] 代理请求: ${targetUrl}`);
        
        try {
            // 解析目标URL
            const target = url.parse(targetUrl);
            const isHttps = target.protocol === 'https:';
            const httpModule = isHttps ? https : http;
            
            // 创建代理请求
            const proxyReq = httpModule.request({
                hostname: target.hostname,
                port: target.port || (isHttps ? 443 : 80),
                path: target.path,
                method: req.method,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/pdf,application/octet-stream,*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            }, (proxyRes) => {
                // 设置响应头
                res.writeHead(proxyRes.statusCode, {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                    'Content-Type': proxyRes.headers['content-type'] || 'application/pdf',
                    'Content-Length': proxyRes.headers['content-length']
                });
                
                // 转发响应数据
                proxyRes.pipe(res);
                
                console.log(`[${new Date().toISOString()}] 响应状态: ${proxyRes.statusCode}`);
            });
            
            // 错误处理
            proxyReq.on('error', (error) => {
                console.error(`[${new Date().toISOString()}] 代理请求错误:`, error);
                if (!res.headersSent) {
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ 
                        error: '代理请求失败', 
                        details: error.message 
                    }));
                }
            });
            
            // 设置超时
            proxyReq.setTimeout(30000, () => {
                console.error(`[${new Date().toISOString()}] 代理请求超时: ${targetUrl}`);
                if (!res.headersSent) {
                    res.writeHead(408, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: '请求超时' }));
                }
                proxyReq.destroy();
            });
            
            // 转发请求数据
            req.pipe(proxyReq);
            
        } catch (error) {
            console.error(`[${new Date().toISOString()}] URL解析错误:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
                error: '无效的URL', 
                details: error.message 
            }));
        }
        
        return;
    }
    
    // 404处理
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`🚀 CORS代理服务器已启动`);
    console.log(`📍 访问地址: http://localhost:${PORT}`);
    console.log(`🔧 代理格式: http://localhost:${PORT}/proxy?url=目标PDF的URL`);
    console.log(`📖 使用说明: http://localhost:${PORT}`);
    console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
    console.log('');
    console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
        console.error('   可以修改代码中的 PORT 变量');
    } else {
        console.error('❌ 服务器错误:', error);
    }
    process.exit(1);
});
