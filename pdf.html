

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Canvas 预览器</title>
    <script src="./jquery-3.6.0.min.js"></script>
    <script src="./pdf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }



        .container {
            max-width: 100vw;
            height: auto;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .page-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .canvas-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
            flex-wrap: wrap;
        }

        .control-buttons {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: white;
            color: #667eea;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            background: #f8f9fa;
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .control-info {
            background: white;
            padding: 10px 20px;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-weight: 500;
            min-width: 80px;
            text-align: center;
        }

        .control-separator {
            width: 1px;
            height: 30px;
            background: #ddd;
            margin: 0 5px;
        }

        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
        }

        .download-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%) !important;
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .page-info {
            background: white;
            padding: 10px 20px;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-weight: 500;
        }



        .canvas-wrapper {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 10px;
        }

        .pdf-canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 100%;
            height: auto;
            display: block;
            /* 提高渲染质量 */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: -webkit-crisp-edges;
            image-rendering: -moz-crisp-edges;
            image-rendering: -o-crisp-edges;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
        }

        .canvas-wrapper {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 10px;
            width: 100%;
            max-width: 100%;
            overflow: hidden;
        }

        .pdf-pages-container {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
        }

        .page-placeholder {
            transition: all 0.3s ease;
            width: 100%;
        }

        .page-placeholder.rendering {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .page-placeholder.rendered {
            background: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }



        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .file-input-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .file-info {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .controls {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
       
            color: #000;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .pdf-container {
            padding: 0 2%;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            overflow: auto;
            height: calc(100vh - 200px);
            min-height: 400px;
            width: 96%;
            margin: 0 auto;
        }

        .pdf-viewer {
            width: 100%;
            height: 100vh;
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background: #f8d7da;
            border-radius: 10px;
            margin: 20px 0;
        }

        .network-error {
            text-align: center;
            padding: 40px 20px;
            color: #0c5460;
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            margin: 20px;
        }

        .network-error .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .network-error h3 {
            color: #0c5460;
            margin-bottom: 20px;
        }

        .network-error .error-details {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .network-error .error-details pre {
            margin: 0;
            white-space: pre-wrap;
            font-family: inherit;
            color: #495057;
        }

        .network-error .error-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .network-help {
            text-align: center;
            padding: 40px 20px;
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            margin: 20px;
        }

        .network-help .help-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .network-help h3 {
            color: #155724;
            margin-bottom: 20px;
        }

        .network-help .help-content {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .network-help .help-content h4 {
            color: #155724;
            margin-bottom: 15px;
        }

        .network-help .help-content ol {
            margin: 0;
            padding-left: 20px;
        }

        .network-help .help-content li {
            margin-bottom: 15px;
        }

        .network-help .help-content ul {
            margin: 10px 0 0 20px;
            padding-left: 0;
        }

        .network-help .help-content ul li {
            margin-bottom: 5px;
        }

        .network-help .help-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .network-status {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }

        .network-test {
            text-align: center;
            padding: 40px 20px;
            color: #0c5460;
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            margin: 20px;
        }

        .network-test .test-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .network-test h3 {
            color: #0c5460;
            margin-bottom: 20px;
        }

        .network-test .test-progress {
            margin: 20px 0;
        }

        .network-test .test-results {
            margin-top: 20px;
        }

        .network-test .test-summary {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .network-test .test-details {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .network-test .test-details ul {
            margin: 0;
            padding-left: 20px;
        }

        .network-test .test-details li {
            margin-bottom: 8px;
        }

        .network-test .test-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .cors-solutions {
            text-align: center;
            padding: 40px 20px;
            color: #721c24;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            margin: 20px;
        }

        .cors-solutions .cors-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .cors-solutions h3 {
            color: #721c24;
            margin-bottom: 20px;
        }

        .cors-solutions .solution-section {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .cors-solutions .solution-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .cors-solutions .solution-option {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #ddd;
        }

        .cors-solutions .solution-option h5 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .cors-solutions .solution-option p {
            margin-bottom: 15px;
            color: #666;
        }

        .cors-solutions .cors-info {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .cors-solutions .cors-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .info-message {
            text-align: center;
            padding: 40px 20px;
            color: #0c5460;
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            margin: 20px;
        }

        .info-message .info-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .info-message h3 {
            color: #0c5460;
            margin-bottom: 20px;
        }

        .info-message .upload-steps {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info-message .upload-steps ol {
            margin: 0;
            padding-left: 20px;
        }

        .info-message .upload-steps li {
            margin-bottom: 10px;
        }

        .cors-help {
            text-align: center;
            padding: 40px 20px;
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            margin: 20px;
        }

        .cors-help .help-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .cors-help h3 {
            color: #155724;
            margin-bottom: 20px;
        }

        .cors-help .help-content {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .cors-help .server-configs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .cors-help .config-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #ddd;
        }

        .cors-help .config-section h5 {
            color: #155724;
            margin-bottom: 10px;
        }

        .cors-help .config-section pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-size: 12px;
            overflow-x: auto;
            margin: 0;
        }

        .cors-help .contact-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .cors-help .contact-info h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .cors-help .help-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .toast-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .url-info .url-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .url-param-indicator {
            color: #007bff;
            font-weight: bold;
            font-size: 12px;
        }

        .url-source {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }

        .test-url-info {
            text-align: center;
            padding: 40px 20px;
            color: #0c5460;
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            margin: 20px;
        }

        .test-url-info h4 {
            color: #0c5460;
            margin-bottom: 20px;
        }

        .test-url-display {
            background: rgba(255,255,255,0.7);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            text-align: left;
        }

        .test-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .parameter-info {
            text-align: center;
            padding: 40px 20px;
            color: #0c5460;
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            margin: 20px;
        }

        .parameter-info .param-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .parameter-info h3 {
            color: #0c5460;
            margin-bottom: 20px;
        }

        .parameter-info .param-details {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .parameter-info .url-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }

        .parameter-info .param-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .load-confirmation {
            text-align: center;
            padding: 40px 20px;
            color: #856404;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            margin: 20px;
        }

        .load-confirmation .confirm-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .load-confirmation h3 {
            color: #856404;
            margin-bottom: 20px;
        }

        .load-confirmation .url-preview {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }

        .load-confirmation .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .url-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }





        .blocked-error {
            color: #856404;
            text-align: center;
            padding: 40px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            margin: 20px;
        }

        .blocked-error h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .blocked-error .btn {
            margin: 5px;
            padding: 10px 20px;
        }

        .page-info {
            text-align: center;
            margin: 15px 0;
            color: #666;
        }





        .tab-container {
            margin: 20px 0;
        }

        .tab-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .input-group {
                flex-direction: column;
                align-items: stretch;
            }
            

            
            .page-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .pdf-container {
                width: 100vw;
                padding: 0 2%;
            }
            
            .canvas-container {
                padding: 10px;
                height: calc(100vh - 150px);
            }
            
            .canvas-wrapper {
                padding: 10px;
                margin: 5px;
            }

            .zoom-controls {
                gap: 5px;
            }

            .zoom-btn {
                width: 35px;
                height: 35px;
                font-size: 16px;
            }

            .page-info {
                font-size: 12px;
                padding: 8px 15px;
            }

            .tab-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
            }

            .header p {
                font-size: 1em;
            }

            .controls-section {
                padding: 20px 15px;
            }

            .input-group {
                gap: 10px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 13px;
            }



            .page-controls {
                gap: 8px;
            }

            .zoom-btn {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .pdf-container {
                width: 96%;
                padding: 0 2%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        





        <!-- Canvas宽度控制按钮 -->
        <div class="canvas-controls" id="canvasControls" style="display: none;">
            <div class="control-buttons">
                <button class="control-btn" id="decreaseWidth" title="减小Canvas宽度">
                    <span style="font-size: 16px;">-</span>
                </button>
                <div class="control-info">
                    <span id="canvasWidth">100%</span>
                </div>
                <button class="control-btn" id="increaseWidth" title="增大Canvas宽度">
                    <span style="font-size: 16px;">✚</span>
                </button>
                <button class="control-btn" id="resetWidth" title="重置Canvas宽度">
                    <span style="font-size: 14px;">↺</span>
                </button>
                <div class="control-separator"></div>
                <button class="control-btn download-btn" id="downloadPDF" onclick="downloadCurrentPDF()" title="下载PDF文件">
                    <span style="font-size: 16px;">⬇</span>
                </button>
            </div>
        </div>

        <div class="pdf-container" id="pdfContainer">
            
        </div>
                </div>
            </div>
        </div>

       
    </div>

    <script>
        // 设置PDF.js worker路径
        pdfjsLib.GlobalWorkerOptions.workerSrc = './pdf.worker.min.js';

        $(document).ready(function() {
            let selectedFile = null;
            let currentPdfUrl = null;
            let pdfDoc = null;
            let currentPage = 1;
            let totalPages = 0;
            let currentZoom = 1.0;
            let canvas = null;
            let ctx = null;
            let currentRenderAbortController = null;
            let renderTimeout = null;
            let baseZoom = 1.0;
            let devicePixelRatio = window.devicePixelRatio || 1;
            let canvasWidthPercent = 100; // Canvas宽度百分比
            let baseCanvasWidth = 100; // 基础Canvas宽度



            // URL参数解析函数
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            // 获取所有URL参数
            function getAllUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                const params = {};
                for (const [key, value] of urlParams.entries()) {
                    params[key] = value;
                }
                return params;
            }

            // 截取URL参数并自动赋值
            function extractAndAssignUrlParameters() {
                const allParams = getAllUrlParameters();
                console.log('所有URL参数:', allParams);
                
                // 支持的PDF URL参数名
                const pdfParamNames = ['pdf', 'url', 'file', 'src', 'link'];
                let foundPdfUrl = null;
                let foundParamName = null;
                
                // 查找PDF URL参数
                for (const paramName of pdfParamNames) {
                    if (allParams[paramName]) {
                        foundPdfUrl = allParams[paramName];
                        foundParamName = paramName;
                        break;
                    }
                }
                
                if (foundPdfUrl) {
                    console.log(`找到PDF URL参数 [${foundParamName}]:`, foundPdfUrl);
                    
                    // 验证和清理URL
                    const validUrl = validateAndCleanUrl(foundPdfUrl);
                    
                    if (validUrl) {
                        loadPDFFromUrl(validUrl);
                    } else {
                        showError(`URL参数 [${foundParamName}] 格式无效。`);
                    }
                } else {
                    console.log('未找到PDF URL参数');
                }
                
                return foundPdfUrl;
            }

          

           





            // URL参数验证和清理
            function validateAndCleanUrl(url) {
                if (!url) return null;
                
                // 解码URL
                try {
                    url = decodeURIComponent(url);
                } catch (e) {
                    console.warn('URL解码失败:', e);
                }
                
                // 验证URL格式
                try {
                    new URL(url);
                } catch (e) {
                    console.error('无效的URL格式:', url);
                    return null;
                }
                
                return url;
            }

            // 检查URL参数并加载PDF
            function checkUrlParameters() {
                // 使用新的截取和赋值功能
                const foundUrl = extractAndAssignUrlParameters();
                
                if (!foundUrl) {
                    console.log('没有URL参数，显示默认状态');
                }
            }

            // 预检测URL是否支持跨域访问
            function checkCorsSupport(url) {
                return new Promise((resolve, reject) => {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒快速检测

                    fetch(url, {
                        method: 'HEAD', // 只检测头部，不下载内容
                        mode: 'cors',
                        signal: controller.signal,
                        credentials: 'omit'
                    })
                    .then(response => {
                        clearTimeout(timeoutId);
                        if (response.ok) {
                            resolve(true); // 支持跨域
                        } else {
                            reject(new Error(`HTTP ${response.status}`));
                        }
                    })
                    .catch(error => {
                        clearTimeout(timeoutId);
                        reject(error); // 不支持跨域或其他错误
                    });
                });
            }

            // 从URL加载PDF
            function loadPDFFromUrl(url) {
                if (!url) {
                    showError('请输入有效的PDF文件地址');
                    return;
                }

                // 将PDF地址赋值给变量currentPdfUrl
                currentPdfUrl = url;
                console.log('设置PDF URL:', currentPdfUrl);

                showLoading('正在检测跨域支持...');

                // 验证URL格式
                try {
                    new URL(url);
                } catch (e) {
                    showError('请输入有效的URL地址');
                    return;
                }

                // 先进行跨域预检测
                checkCorsSupport(url)
                    .then(() => {
                        console.log('检测到CORS支持，直接加载');
                        showLoading('正在从URL加载PDF...');

                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 15000);
                        loadPDFDirect(url, controller, timeoutId)
                            .catch(error => {
                                console.log('直接加载失败，尝试代理服务:', error);
                                return loadPDFWithProxy(url, controller, timeoutId);
                            })
                            .catch(error => {
                                showCorsSolutions(url, error);
                            });
                    })
                    .catch(error => {
                        console.log('检测到跨域限制，直接使用代理服务:', error);
                        showLoading('正在通过代理服务加载PDF...');

                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 15000);
                        loadPDFWithProxy(url, controller, timeoutId)
                            .catch(error => {
                                showCorsSolutions(url, error);
                            });
                    });
            }

            // 带降级方案的PDF加载
            function loadPDFWithFallback(url, controller, timeoutId) {
                const startTime = Date.now();
                console.log('开始加载PDF:', url);
                
                // 首先尝试直接加载
                loadPDFDirect(url, controller, timeoutId)
                    .then(() => {
                        const totalTime = Date.now() - startTime;
                        console.log(`直接加载成功，耗时: ${totalTime}ms`);
                    })
                    .catch(error => {
                        const directFailTime = Date.now() - startTime;
                        console.log(`直接加载失败 (${directFailTime}ms)，尝试代理服务:`, error);
                        // 如果直接加载失败，尝试使用代理服务
                        return loadPDFWithProxy(url, controller, timeoutId);
                    })
                    .catch(error => {
                        const totalFailTime = Date.now() - startTime;
                        console.log(`代理服务也失败 (${totalFailTime}ms)，尝试其他方案:`, error);
                        // 如果代理也失败，显示跨域解决方案
                        showCorsSolutions(url, error);
                    });
            }

            // 直接加载PDF
            function loadPDFDirect(url, controller, timeoutId) {
                return new Promise((resolve, reject) => {
                    const startTime = Date.now();
                    
                    fetch(url, {
                        method: 'GET',
                        mode: 'cors',
                        signal: controller.signal,
                        credentials: 'omit',
                        headers: {
                            'Accept': 'application/pdf,application/octet-stream,*/*',
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        }
                    })
                    .then(response => {
                        clearTimeout(timeoutId);
                        const responseTime = Date.now() - startTime;
                        console.log(`直接加载响应时间: ${responseTime}ms`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        const contentType = response.headers.get('content-type');
                        if (contentType && !contentType.includes('application/pdf') && !contentType.includes('octet-stream')) {
                            console.warn('警告：响应内容类型不是PDF格式');
                        }
                        return response.arrayBuffer();
                    })
                    .then(arrayBuffer => {
                        const totalTime = Date.now() - startTime;
                        console.log(`直接加载完成，总耗时: ${totalTime}ms`);
                        
                        if (arrayBuffer.byteLength === 0) {
                            throw new Error('PDF文件为空');
                        }
                        const typedArray = new Uint8Array(arrayBuffer);
                        loadPDFDocument(typedArray);
                        
                        resolve();
                    })
                    .catch(error => {
                        const failTime = Date.now() - startTime;
                        console.log(`直接加载失败，耗时: ${failTime}ms`, error);
                        reject(error);
                    });
                });
            }
                
                            // 使用代理服务加载PDF
            function loadPDFWithProxy(url, controller, timeoutId) {
                return new Promise((resolve, reject) => {
                    // 优化的代理服务列表 - 按速度和可靠性排序
                    const proxyServices = [
                        // 快速代理服务
                        `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`,
                        `https://thingproxy.freeboard.io/fetch/${url}`,
                        `https://api.codetabs.com/v1/proxy?quest=${encodeURIComponent(url)}`,

                        // 备用代理服务
                        `https://cors.bridged.cc/${url}`,
                        `https://cors-anywhere.herokuapp.com/${url}`,

                        // 新增更多代理服务
                        `https://proxy.cors.sh/${url}`,
                        `https://yacdn.org/proxy/${url}`,
                        `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}&disableCache=true`,

                        // 备用方案
                        `https://crossorigin.me/${url}`,
                        `https://cors-proxy.htmldriven.com/?url=${encodeURIComponent(url)}`
                    ];
                    
                    let currentProxyIndex = 0;
                    let startTime = Date.now();
                    
                    function tryNextProxy() {
                        if (currentProxyIndex >= proxyServices.length) {
                            reject(new Error('所有代理服务都失败了'));
                            return;
                        }
                        
                        const proxyUrl = proxyServices[currentProxyIndex];
                        const proxyStartTime = Date.now();
                        console.log(`尝试代理服务 ${currentProxyIndex + 1}: ${proxyUrl}`);
                        
                        // 为每个代理设置较短的超时时间
                        const proxyTimeout = 10000; // 10秒超时
                        const proxyController = new AbortController();
                        const proxyTimeoutId = setTimeout(() => proxyController.abort(), proxyTimeout);
                        
                        fetch(proxyUrl, {
                            method: 'GET',
                            signal: proxyController.signal,
                            headers: {
                                'Accept': 'application/pdf,application/octet-stream,*/*',
                                'Cache-Control': 'no-cache',
                                'Pragma': 'no-cache'
                            }
                        })
                        .then(response => {
                            clearTimeout(proxyTimeoutId);
                            const responseTime = Date.now() - proxyStartTime;
                            console.log(`代理服务 ${currentProxyIndex + 1} 响应时间: ${responseTime}ms`);
                            
                            if (!response.ok) {
                                throw new Error(`代理服务错误: ${response.status}`);
                            }
                            return response.arrayBuffer();
                        })
                        .then(arrayBuffer => {
                            const totalTime = Date.now() - startTime;
                            console.log(`代理加载成功，总耗时: ${totalTime}ms`);
                            
                            if (arrayBuffer.byteLength === 0) {
                                throw new Error('PDF文件为空');
                            }
                            const typedArray = new Uint8Array(arrayBuffer);
                            loadPDFDocument(typedArray);
                            resolve();
                        })
                        .catch(error => {
                            clearTimeout(proxyTimeoutId);
                            const failTime = Date.now() - proxyStartTime;
                            console.log(`代理服务 ${currentProxyIndex + 1} 失败 (${failTime}ms):`, error);
                            currentProxyIndex++;
                            
                            // 快速切换到下一个代理
                            setTimeout(tryNextProxy, 100);
                        });
                    }
                    
                    tryNextProxy();
                });
            }

            // 切换到文件上传
            function switchToFileUpload() {
                $('#file-tab').click();
                $('#pdfContainer').html(`
                    <div class="info-message">
                        <div class="info-icon">📁</div>
                        <h3>请使用文件上传</h3>
                        <p>由于跨域限制，建议您将PDF文件下载到本地，然后使用文件上传功能。</p>
                        <div class="upload-steps">
                            <ol>
                                <li>右键点击PDF链接，选择"另存为"</li>
                                <li>将文件保存到本地</li>
                                <li>点击"选择PDF文件"按钮</li>
                                <li>选择刚才保存的PDF文件</li>
                            </ol>
                        </div>
                    </div>
                `);
            }

            // 在新窗口中打开PDF
            function openPDFInNewWindow(url) {
                window.open(url, '_blank');
            }

            // 显示浏览器扩展推荐
            function showBrowserExtensions() {
                $('#pdfContainer').html(`
                    <div class="cors-help">
                        <div class="help-icon">🔌</div>
                        <h3>浏览器扩展解决方案</h3>
                        <div class="help-content">
                            <h4>推荐的CORS扩展：</h4>
                            <div class="server-configs">
                                <div class="config-section">
                                    <h5>Chrome浏览器</h5>
                                    <ul style="text-align: left; margin: 10px 0;">
                                        <li><strong>CORS Unblock</strong> - 简单易用的CORS禁用扩展</li>
                                        <li><strong>Disable Web Security</strong> - 临时禁用网页安全限制</li>
                                        <li><strong>CORS Toggle</strong> - 一键开关CORS限制</li>
                                    </ul>
                                    <button class="btn btn-primary btn-sm" onclick="window.open('https://chrome.google.com/webstore/search/cors', '_blank')">
                                        访问Chrome应用商店
                                    </button>
                                </div>

                                <div class="config-section">
                                    <h5>Firefox浏览器</h5>
                                    <ul style="text-align: left; margin: 10px 0;">
                                        <li><strong>CORS Everywhere</strong> - Firefox专用CORS扩展</li>
                                        <li><strong>Disable CORS</strong> - 临时禁用跨域限制</li>
                                    </ul>
                                    <button class="btn btn-primary btn-sm" onclick="window.open('https://addons.mozilla.org/search/?q=cors', '_blank')">
                                        访问Firefox附加组件
                                    </button>
                                </div>

                                <div class="config-section">
                                    <h5>本地代理服务器</h5>
                                    <p style="margin: 10px 0; font-size: 14px;">使用项目中的 cors-proxy-server.js</p>
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0;">
                                        node cors-proxy-server.js<br>
                                        然后访问: http://localhost:3000/proxy?url=PDF地址
                                    </div>
                                    <button class="btn btn-success btn-sm" onclick="showLocalProxyHelp()">
                                        查看详细说明
                                    </button>
                                </div>
                            </div>

                            <div class="contact-info" style="background: #fff3cd; border: 1px solid #ffeaa7;">
                                <h4 style="color: #856404;">⚠️ 重要提醒：</h4>
                                <ul style="text-align: left; margin: 10px 0; color: #856404;">
                                    <li>这些扩展会降低浏览器安全性</li>
                                    <li>仅在开发或测试时使用</li>
                                    <li>使用完毕后请及时禁用或卸载</li>
                                    <li>不要在处理敏感信息时使用</li>
                                </ul>
                            </div>
                        </div>

                        <div class="help-actions">
                            <button class="btn btn-primary" onclick="switchToFileUpload()">使用文件上传</button>
                            <button class="btn btn-outline" onclick="showCorsSolutions('${currentPdfUrl}', null)">返回解决方案</button>
                        </div>
                    </div>
                `);
            }

            // 显示本地代理服务器帮助
            function showLocalProxyHelp() {
                $('#pdfContainer').html(`
                    <div class="cors-help">
                        <div class="help-icon">🖥️</div>
                        <h3>本地代理服务器使用指南</h3>
                        <div class="help-content">
                            <h4>步骤1：启动代理服务器</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                <p style="margin-bottom: 10px;">在项目目录中打开命令行，运行：</p>
                                <div style="background: #343a40; color: #fff; padding: 10px; border-radius: 4px; font-family: monospace;">
                                    node cors-proxy-server.js
                                </div>
                                <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                    如果没有安装Node.js，请先从 <a href="https://nodejs.org" target="_blank">nodejs.org</a> 下载安装
                                </p>
                            </div>

                            <h4>步骤2：使用代理地址</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                <p style="margin-bottom: 10px;">将PDF的URL转换为代理地址：</p>
                                <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                                    原地址：https://example.com/document.pdf<br>
                                    代理地址：http://localhost:3000/proxy?url=https://example.com/document.pdf
                                </div>
                            </div>

                            <h4>步骤3：在预览器中使用</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                <p>将代理地址粘贴到PDF预览器的URL输入框中，或者作为URL参数：</p>
                                <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all;">
                                    pdf.html?url=http://localhost:3000/proxy?url=https://example.com/document.pdf
                                </div>
                            </div>

                            <div class="contact-info">
                                <h4>✅ 优势：</h4>
                                <ul style="text-align: left; margin: 10px 0;">
                                    <li>完全控制代理逻辑</li>
                                    <li>不依赖第三方服务</li>
                                    <li>可以自定义请求头</li>
                                    <li>支持调试和日志</li>
                                    <li>更高的安全性和隐私保护</li>
                                </ul>
                            </div>
                        </div>

                        <div class="help-actions">
                            <button class="btn btn-primary" onclick="switchToFileUpload()">使用文件上传</button>
                            <button class="btn btn-secondary" onclick="showBrowserExtensions()">返回扩展推荐</button>
                            <button class="btn btn-outline" onclick="showCorsSolutions('${currentPdfUrl}', null)">返回解决方案</button>
                        </div>
                    </div>
                `);
            }

            // 从URL加载PDF
            function loadPDFFromUrl(url) {
                if (!url) {
                    showError('请输入有效的PDF文件地址');
                    return;
                }

                // 将PDF地址赋值给变量currentPdfUrl
                currentPdfUrl = url;
                console.log('设置PDF URL:', currentPdfUrl);

                showLoading('正在检测跨域支持...');

                // 验证URL格式
                try {
                    new URL(url);
                } catch (e) {
                    showError('请输入有效的URL地址');
                    return;
                }

                // 先进行跨域预检测
                checkCorsSupport(url)
                    .then(() => {
                        console.log('检测到CORS支持，直接加载');
                        showLoading('正在从URL加载PDF...');

                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 15000);
                        loadPDFDirect(url, controller, timeoutId)
                            .catch(error => {
                                console.log('直接加载失败，尝试代理服务:', error);
                                return loadPDFWithProxy(url, controller, timeoutId);
                            })
                            .catch(error => {
                                showCorsSolutions(url, error);
                            });
                    })
                    .catch(error => {
                        console.log('检测到跨域限制，直接使用代理服务:', error);
                        showLoading('正在通过代理服务加载PDF...');

                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 15000);
                        loadPDFWithProxy(url, controller, timeoutId)
                            .catch(error => {
                                showCorsSolutions(url, error);
                            });
                    });
            }

       

            // 显示CORS配置帮助
            function showCorsHelp() {
                $('#pdfContainer').html(`
                    <div class="cors-help">
                        <div class="help-icon">🔧</div>
                        <h3>CORS配置帮助</h3>
                        <div class="help-content">
                            <h4>服务器端CORS配置：</h4>
                            <div class="server-configs">
                                <div class="config-section">
                                    <h5>Apache (.htaccess)</h5>
                                    <pre>Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type"</pre>
                                </div>
                                
                                <div class="config-section">
                                    <h5>Nginx</h5>
                                    <pre>location / {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type";
}</pre>
                                </div>
                                
                                <div class="config-section">
                                    <h5>Node.js (Express)</h5>
                                    <pre>app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type');
    next();
});</pre>
                                </div>
                                
                                <div class="config-section">
                                    <h5>PHP</h5>
                                    <pre>&lt;?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
?&gt;</pre>
                                </div>
                            </div>
                            
                            <div class="config-section">
                                <h5>本地开发服务器</h5>
                                <pre># Python 3 (推荐)
python -m http.server 8000 --bind 0.0.0.0

# Python 2
python -m SimpleHTTPServer 8000

# Node.js (需要安装 http-server)
npm install -g http-server
http-server -p 8000 --cors

# PHP
php -S localhost:8000</pre>
                                <p style="font-size: 12px; margin-top: 10px;">启动后访问: http://localhost:8000/pdf.html</p>
                            </div>

                            <div class="contact-info">
                                <h4>联系网站管理员：</h4>
                                <p>请将以上配置信息发送给网站管理员，请求他们配置CORS头以允许跨域访问PDF文件。</p>
                            </div>
                        </div>
                        
                        <div class="help-actions">
                            <button class="btn btn-primary" onclick="switchToFileUpload()">使用文件上传</button>
                            <button class="btn btn-outline" onclick="location.reload()">返回</button>
                        </div>
                    </div>
                `);
            }

            // 显示跨域解决方案
            function showCorsSolutions(url, error) {
                $('#pdfContainer').html(`
                    <div class="cors-solutions">
                        <div class="cors-icon">🚫</div>
                        <h3>跨域访问限制</h3>
                        <p>由于浏览器的同源策略，无法直接访问该PDF文件。</p>
                        
                        <div class="solution-section">
                            <h4>解决方案：</h4>
                            <div class="solution-options">
                                <div class="solution-option">
                                    <h5>1. 使用本地文件上传</h5>
                                    <p>将PDF文件下载到本地，然后使用文件上传功能</p>
                                    <button class="btn btn-primary" onclick="switchToFileUpload()">切换到文件上传</button>
                                </div>
                                
                                <div class="solution-option">
                                    <h5>2. 在新窗口中打开</h5>
                                    <p>直接在新浏览器窗口中打开PDF文件</p>
                                    <button class="btn btn-secondary" onclick="openPDFInNewWindow('${url}')">在新窗口打开</button>
                                </div>
                                
                                <div class="solution-option">
                                    <h5>3. 下载PDF文件</h5>
                                    <p>下载PDF文件到本地后查看</p>
                                    <button class="btn btn-outline" onclick="downloadPDFDirectly('${url}')">下载PDF</button>
                                </div>
                                
                                <div class="solution-option">
                                    <h5>4. 使用浏览器扩展</h5>
                                    <p>安装CORS扩展临时禁用跨域限制</p>
                                    <button class="btn btn-secondary" onclick="showBrowserExtensions()">查看扩展推荐</button>
                                </div>

                                <div class="solution-option">
                                    <h5>5. 联系网站管理员</h5>
                                    <p>请求配置CORS头以允许跨域访问</p>
                                    <button class="btn btn-outline" onclick="showCorsHelp()">查看CORS配置帮助</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="cors-info">
                            <h4>技术说明：</h4>
                            <p>跨域资源共享(CORS)是一种安全机制，防止网页从不同域名加载资源。要解决此问题，服务器需要配置适当的CORS头。</p>
                        </div>
                        
                        <div class="cors-actions">
                            <button class="btn btn-primary" onclick="retryLoad()">重试</button>
                            <button class="btn btn-outline" onclick="location.reload()">返回</button>
                        </div>
                    </div>
                `);
            }





            // 显示提示消息
            function showToast(message) {
                const toast = $(`
                    <div class="toast-message">
                        ${message}
                    </div>
                `);
                $('body').append(toast);
                setTimeout(() => {
                    toast.fadeOut(() => toast.remove());
                }, 3000);
            }









            // 文件选择处理
            $('#pdfFile').on('change', function(e) {
                const file = e.target.files[0];
                if (file && file.type === 'application/pdf') {
                    loadPDFFromFile(file);
                } else {
                    showError('请选择有效的PDF文件！');
                }
            });

            // 从文件加载PDF
            function loadPDFFromFile(file) {
                // 将文件信息保存到selectedFile变量
                selectedFile = file;
                // 清空URL，因为这是本地文件
                currentPdfUrl = null;
                console.log('设置本地文件:', selectedFile.name);

                showLoading('正在加载PDF文件...');
                showFileInfo(file);
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const arrayBuffer = e.target.result;
                        if (arrayBuffer.byteLength === 0) {
                            throw new Error('PDF文件为空');
                        }
                        const typedArray = new Uint8Array(arrayBuffer);
                        loadPDFDocument(typedArray);
                    } catch (error) {
                        console.error('文件处理错误:', error);
                        showError('处理PDF文件时发生错误：' + error.message);
                    }
                };
                reader.onerror = function(error) {
                    console.error('文件读取错误:', error);
                    showError('读取PDF文件时发生错误，请确保文件未损坏且格式正确');
                };
                reader.onabort = function() {
                    showError('文件读取被中断');
                };
                reader.readAsArrayBuffer(file);
            }

            // 加载PDF文档
            function loadPDFDocument(data) {
                // 验证PDF文件头
                const pdfHeader = new Uint8Array(data.slice(0, 5));
                const headerString = String.fromCharCode.apply(null, pdfHeader);
                if (headerString !== '%PDF-') {
                    showError('无效的PDF文件：文件格式不正确');
                    return;
                }

                pdfjsLib.getDocument({
                    data: data,
                    cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
                    cMapPacked: true,
                    // 提高渲染质量设置
                    verbosity: 0, // 减少日志输出
                    maxImageSize: -1, // 不限制图片大小
                    cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
                    cMapPacked: true,
                    standardFontDataUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/standard_fonts/'
                }).promise
                    .then(function(pdf) {
                        pdfDoc = pdf;
                        totalPages = pdf.numPages;
                        currentPage = 1;
                        
                        if (totalPages === 0) {
                            throw new Error('PDF文件没有页面');
                        }
                        
                        updatePageInfo();
                        renderWithScrollLoading();
                        showFileInfo(null, totalPages);
                        
                        // 显示Canvas宽度控制面板
                        $('#canvasControls').show();
                    })
                    .catch(function(error) {
                        console.error('PDF解析错误:', error);
                        if (error.name === 'PasswordException') {
                            showError('PDF文件已加密，需要密码才能打开');
                        } else if (error.name === 'InvalidPDFException') {
                            showError('无效的PDF文件：文件可能已损坏或格式不正确');
                        } else if (error.name === 'MissingPDFException') {
                            showError('PDF文件缺失或无法访问');
                        } else if (error.name === 'UnexpectedResponseException') {
                            showError('服务器响应异常，请检查PDF文件URL');
                        } else {
                            showError('解析PDF文件时发生错误：' + error.message);
                        }
                    });
            }

                        // 滑动加载渲染
            function renderWithScrollLoading() {
                if (!pdfDoc) return;

                // 取消之前的渲染操作
                if (currentRenderAbortController) {
                    currentRenderAbortController.abort();
                }
                
                // 创建新的AbortController
                currentRenderAbortController = new AbortController();
                
                showLoading('正在准备PDF预览...');
                
                // 创建容器
                $('#pdfContainer').html(`
                    <div class="canvas-wrapper" id="canvasWrapper">
                        <div class="pdf-pages-container" id="pdfPagesContainer"></div>
                    </div>
                `);
                
                const pagesContainer = $('#pdfPagesContainer');
                let totalHeight = 0;
                let maxWidth = 0;
                let pageData = [];
                
                // 先计算所有页面的尺寸
                const pagePromises = [];
                for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                    pagePromises.push(
                        pdfDoc.getPage(pageNum).then(function(page) {
                            // 获取原始视口
                            const originalViewport = page.getViewport({scale: 1.0});
                            
                            // 使用固定缩放
                            if (pageNum === 1) {
                                baseZoom = calculateFixedZoom(originalViewport);
                                currentZoom = baseZoom;
                               
                            }
                            
                            const viewport = page.getViewport({scale: currentZoom});
                            totalHeight += viewport.height;
                            maxWidth = Math.max(maxWidth, viewport.width);
                            return { page, viewport, pageNum, originalViewport };
                        })
                    );
                }
                
                // 计算页面尺寸
                Promise.all(pagePromises).then(function(data) {
                    pageData = data;
                    
                                    // 设置容器尺寸 - 使用动态宽度
                const containerWidth = $('#pdfContainer').width() - 80;
                const adjustedWidth = (containerWidth * canvasWidthPercent) / 100;
                pagesContainer.css({
                    'width': adjustedWidth + 'px',
                    'height': totalHeight + 'px',
                    'position': 'relative',
                    'margin': '0 auto'
                });
                    
                    // 创建页面占位符
                    let currentY = 0;
                    pageData.forEach(function(data) {
                        const { viewport, pageNum } = data;
                        
                        const pagePlaceholder = $(`
                            <div class="page-placeholder" data-page="${pageNum}" style="
                                position: absolute;
                                top: ${currentY}px;
                                left: 0;
                                width: 100%;
                                height: ${viewport.height}px;
                                background: white;
                                border-bottom: 1px solid #ddd;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #999;
                                font-size: 14px;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            ">
                                <div>第 ${pageNum} 页 - 加载中...</div>
                            </div>
                        `);
                        
                        pagesContainer.append(pagePlaceholder);
                        currentY += viewport.height;
                    });
                    
                    hideLoading();
                    
                    // 开始监听滚动
                    setupScrollListener(pageData);
                    
                    // 立即渲染可见页面
                    renderVisiblePages(pageData);
                    
                }).catch(function(error) {
                    console.error('页面计算错误:', error);
                    showError('处理PDF页面时发生错误：' + error.message);
                });
            }
            
            // 设置滚动监听
            function setupScrollListener(pageData) {
                const container = $('#pdfContainer');
                let scrollTimeout = null;
                
                container.on('scroll', function() {
                    if (scrollTimeout) {
                        clearTimeout(scrollTimeout);
                    }
                    
                    scrollTimeout = setTimeout(function() {
                        renderVisiblePages(pageData);
                    }, 100);
                });
                
                // 监听窗口大小变化
                $(window).on('resize', function() {
                    if (scrollTimeout) {
                        clearTimeout(scrollTimeout);
                    }
                    
                    scrollTimeout = setTimeout(function() {
                        // 只重新渲染可见页面，不改变缩放
                        if (pageData && pageData.length > 0) {
                            renderVisiblePages(pageData);
                        }
                        
                        // 确保所有已渲染的canvas占据100%宽度
                        $('.page-placeholder.rendered canvas').css({
                            'width': '100%',
                            'height': 'auto',
                            'display': 'block'
                        });
                    }, 300);
                });
            }
            
            // 渲染可见页面
            function renderVisiblePages(pageData) {
                if (!pageData || pageData.length === 0) return;
                
                const container = $('#pdfContainer');
                const containerTop = container.scrollTop();
                const containerHeight = container.height();
                
                // 计算可见区域（包含预加载区域）
                const preloadDistance = containerHeight * 2; // 预加载2屏距离
                const visibleTop = containerTop - preloadDistance;
                const visibleBottom = containerTop + containerHeight + preloadDistance;
                
                // 检查每个页面是否在可见区域内
                pageData.forEach(function(data) {
                    const { page, viewport, pageNum } = data;
                    const pagePlaceholder = $(`.page-placeholder[data-page="${pageNum}"]`);
                    
                    if (pagePlaceholder.length === 0) return;
                    
                    const placeholderTop = parseInt(pagePlaceholder.css('top'));
                    const placeholderBottom = placeholderTop + viewport.height;
                    
                    // 检查页面是否在预加载区域内
                    const isInPreloadArea = (
                        (placeholderTop >= visibleTop && placeholderTop <= visibleBottom) ||
                        (placeholderBottom >= visibleTop && placeholderBottom <= visibleBottom) ||
                        (placeholderTop <= visibleTop && placeholderBottom >= visibleBottom)
                    );
                    
                    // 如果页面在预加载区域且未渲染，则渲染
                    if (isInPreloadArea && !pagePlaceholder.hasClass('rendered') && !pagePlaceholder.hasClass('rendering')) {
                        renderPage(page, viewport, pageNum, pagePlaceholder);
                    }
                });
            }
            
            // 渲染单个页面
            function renderPage(page, viewport, pageNum, placeholder) {
                // 标记为正在渲染
                placeholder.addClass('rendering');
                
                // 创建canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 获取占位符的实际宽度 - 使用100%宽度
                const placeholderWidth = placeholder.width();
                const placeholderHeight = placeholder.height();
                
                // 提高渲染清晰度 - 使用设备像素比
                const pixelRatio = window.devicePixelRatio || 1;
                const scaleFactor = Math.max(1.5, pixelRatio); // 最小1.5倍，确保清晰度
                
                // 设置canvas的实际尺寸（高分辨率）
                canvas.width = placeholderWidth * scaleFactor;
                canvas.height = placeholderHeight * scaleFactor;
                
                // 设置canvas的显示尺寸
                canvas.style.width = canvasWidthPercent + '%';
                canvas.style.height = 'auto';
                canvas.style.display = 'block';
                
                // 缩放绘图上下文以匹配高分辨率
                ctx.scale(scaleFactor, scaleFactor);
                
                // 计算缩放比例 - 基于100%宽度
                const scaleX = placeholderWidth / viewport.width;
                const scaleY = placeholderHeight / viewport.height;
                const scale = Math.min(scaleX, scaleY);
                
                // 创建适配的视口
                const adaptedViewport = page.getViewport({scale: currentZoom * scale});
                
                const renderContext = {
                    canvasContext: ctx,
                    viewport: adaptedViewport,
                    enableWebGL: true, // 启用WebGL加速
                    renderInteractiveForms: true // 渲染交互式表单
                };
                
                            page.render(renderContext).promise.then(function() {
                // 检查是否被取消
                if (currentRenderAbortController.signal.aborted) {
                    return;
                }
                
                // 替换占位符为canvas
                placeholder.html(canvas);
                placeholder.removeClass('rendering').addClass('rendered');
                
                // 确保canvas占据动态宽度
                canvas.style.width = canvasWidthPercent + '%';
                canvas.style.height = 'auto';
                canvas.style.display = 'block';
                    
                    // 更新进度
                    const renderedPages = $('.page-placeholder.rendered').length;
                    updateProgress((renderedPages / totalPages) * 100);
                    
                }).catch(function(error) {
                    if (!currentRenderAbortController.signal.aborted) {
                        console.error(`页面 ${pageNum} 渲染错误:`, error);
                        placeholder.html('<div style="color: red;">渲染失败</div>');
                        placeholder.removeClass('rendering');
                    }
                });
            }

            // 隐藏加载状态
            function hideLoading() {
                // 渲染完成，隐藏加载状态
            }

            // 更新页面信息
            function updatePageInfo() {
                $('#totalPages').text(totalPages);
            }

            // 显示文件信息
            function showFileInfo(file, pageCount) {
                if (file) {
                    const fileName = file.name;
                    const fileSize = formatFileSize(file.size);
                    
                    $('#fileName').text(fileName);
                    $('#fileSize').text(fileSize);
                }
                
                if (pageCount) {
                    $('#pageCount').text(pageCount + ' 页');
                }
                
                $('#fileInfo').addClass('show');
            }

            // 更新进度条
            function updateProgress(percent) {
                $('#progressFill').css('width', percent + '%');
                
                // 更新已渲染页面数
                const renderedPages = $('.page-placeholder.rendered').length;
                $('#renderedPages').text(renderedPages);
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 重置文件输入
            function resetFileInput() {
                $('#pdfFile').val('');
                selectedFile = null;
                $('#fileInfo').removeClass('show');
                $('#previewBtn').hide();
                $('#demoBtn').show();
                // showLoading('请选择PDF文件进行预览');
            }

            // 显示加载状态
            function showLoading(message) {
                $('#pdfContainer').html(`
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>${message}</p>
                    </div>
                `);
            }

            // 显示错误信息
            function showError(message) {
                $('#pdfContainer').html(`
                    <div class="error">
                        <h3>❌ 错误</h3>
                        <p>${message}</p>
                    </div>
                `);
            }

            // 显示网络错误信息
            function showNetworkError(title, details) {
                $('#pdfContainer').html(`
                    <div class="network-error">
                        <div class="error-icon">🌐</div>
                        <h3>${title}</h3>
                        <div class="error-details">
                            <pre>${details}</pre>
                        </div>
                        <div class="error-actions">
                            <button class="btn btn-primary" onclick="retryLoad()">重试</button>
                            <button class="btn btn-secondary" onclick="showNetworkHelp()">网络帮助</button>
                            <button class="btn btn-outline" onclick="location.reload()">刷新页面</button>
                        </div>
                    </div>
                `);
            }

            // 重试加载
            function retryLoad() {
                const url = $('#pdfUrl').val().trim();
                if (url) {
                    loadPDFFromUrl(url);
                } else {
                    showError('请先输入PDF文件URL');
                }
            }

            // 检测网络状态
            function checkNetworkStatus() {
                if (!navigator.onLine) {
                    return '离线';
                }
                
                if ('connection' in navigator) {
                    const connection = navigator.connection;
                    if (connection.effectiveType) {
                        return `${connection.effectiveType} (${connection.downlink}Mbps)`;
                    }
                }
                
                return '在线';
            }

            // 测试网络连接
            function testNetwork() {
                const testUrls = [
                    'https://www.google.com',
                    'https://www.baidu.com',
                    'https://www.bing.com'
                ];
                
                let testResults = [];
                let completedTests = 0;
                
                $('#pdfContainer').html(`
                    <div class="network-test">
                        <div class="test-icon">🔍</div>
                        <h3>网络连接测试</h3>
                        <div class="test-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <p>正在测试网络连接...</p>
                        </div>
                        <div class="test-results" id="testResults"></div>
                    </div>
                `);
                
                testUrls.forEach((url, index) => {
                    const startTime = Date.now();
                    fetch(url, { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache'
                    })
                    .then(() => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        testResults.push({
                            url: url,
                            status: '成功',
                            responseTime: responseTime
                        });
                    })
                    .catch(() => {
                        testResults.push({
                            url: url,
                            status: '失败',
                            responseTime: null
                        });
                    })
                    .finally(() => {
                        completedTests++;
                        const progress = (completedTests / testUrls.length) * 100;
                        $('.progress-fill').css('width', progress + '%');
                        
                        if (completedTests === testUrls.length) {
                            displayTestResults(testResults);
                        }
                    });
                });
            }

            // 显示测试结果
            function displayTestResults(results) {
                const successCount = results.filter(r => r.status === '成功').length;
                const avgResponseTime = results
                    .filter(r => r.responseTime)
                    .reduce((sum, r) => sum + r.responseTime, 0) / results.filter(r => r.responseTime).length;
                
                let resultsHtml = `
                    <div class="test-summary">
                        <h4>测试结果</h4>
                        <p><strong>成功连接：</strong> ${successCount}/${results.length}</p>
                        <p><strong>平均响应时间：</strong> ${avgResponseTime ? Math.round(avgResponseTime) + 'ms' : 'N/A'}</p>
                    </div>
                    <div class="test-details">
                        <h4>详细结果：</h4>
                        <ul>
                `;
                
                results.forEach(result => {
                    const statusIcon = result.status === '成功' ? '✅' : '❌';
                    const timeInfo = result.responseTime ? ` (${result.responseTime}ms)` : '';
                    resultsHtml += `<li>${statusIcon} ${result.url} - ${result.status}${timeInfo}</li>`;
                });
                
                resultsHtml += `
                        </ul>
                    </div>
                    <div class="test-actions">
                        <button class="btn btn-primary" onclick="retryLoad()">重试加载PDF</button>
                        <button class="btn btn-outline" onclick="showNetworkHelp()">返回帮助</button>
                    </div>
                `;
                
                $('.test-results').html(resultsHtml);
                $('.test-progress').hide();
            }

            // 显示网络帮助
            function showNetworkHelp() {
                const networkStatus = checkNetworkStatus();
                $('#pdfContainer').html(`
                    <div class="network-help">
                        <div class="help-icon">💡</div>
                        <h3>网络问题解决方案</h3>
                        <div class="network-status">
                            <strong>当前网络状态：</strong> ${networkStatus}
                        </div>
                        <div class="help-content">
                            <h4>常见问题及解决方法：</h4>
                            <ol>
                                <li><strong>网络连接问题</strong>
                                    <ul>
                                        <li>检查网络连接是否正常</li>
                                        <li>尝试访问其他网站确认网络状态</li>
                                        <li>重启路由器或切换网络</li>
                                        <li>检查防火墙设置</li>
                                    </ul>
                                </li>
                                <li><strong>URL地址问题</strong>
                                    <ul>
                                        <li>确认URL地址完整且正确</li>
                                        <li>检查是否包含协议（http:// 或 https://）</li>
                                        <li>验证文件扩展名是否为.pdf</li>
                                        <li>确保URL没有特殊字符</li>
                                    </ul>
                                </li>
                                <li><strong>跨域访问限制</strong>
                                    <ul>
                                        <li>服务器需要配置CORS头</li>
                                        <li>使用代理服务器中转</li>
                                        <li>联系网站管理员</li>
                                        <li>尝试使用本地文件上传</li>
                                    </ul>
                                </li>
                                <li><strong>服务器问题</strong>
                                    <ul>
                                        <li>服务器可能暂时不可用</li>
                                        <li>稍后重试</li>
                                        <li>联系服务器管理员</li>
                                        <li>检查服务器负载</li>
                                    </ul>
                                </li>
                                <li><strong>文件大小问题</strong>
                                    <ul>
                                        <li>文件可能过大导致超时</li>
                                        <li>尝试压缩PDF文件</li>
                                        <li>使用本地文件上传</li>
                                        <li>联系管理员优化文件</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                        <div class="help-actions">
                            <button class="btn btn-primary" onclick="retryLoad()">重试</button>
                            <button class="btn btn-secondary" onclick="testNetwork()">测试网络</button>
                            <button class="btn btn-outline" onclick="location.reload()">返回</button>
                        </div>
                    </div>
                `);
            }





            // 计算固定缩放比例
            function calculateFixedZoom(viewport) {
                // 使用固定的缩放比例，不进行自适应
                return 1.0;
            }

            // 防抖渲染函数
            function debouncedRender() {
                if (renderTimeout) {
                    clearTimeout(renderTimeout);
                }
                renderTimeout = setTimeout(function() {
                    if (pdfDoc) {
                        renderWithScrollLoading();
                    }
                    renderTimeout = null;
                }, 300);
            }



            // 加载示例PDF
            $('#demoBtn').on('click', function() {
                loadDemoPDF();
            });







            // 隐藏加载状态
            function hideLoading() {
                // PDF加载成功，隐藏加载状态
            }

            // 显示PDF加载错误
            function showPDFError() {
                showError('PDF文件加载失败，可能是由于浏览器安全策略限制。请尝试在新窗口中打开PDF文件。');
            }

            // 显示被屏蔽的PDF并提供替代方案
            function showBlockedPDF(url) {
                $('#pdfContainer').html(`
                    <div class="blocked-error">
                        <h3>⚠️ 浏览器安全限制</h3>
                        <p>由于浏览器安全策略，PDF无法在此页面中直接显示。</p>
                        <div style="margin: 20px 0;">
                            <button class="btn btn-primary" onclick="openPDFInNewWindow('${url}')">
                                在新窗口中打开
                            </button>
                            <button class="btn btn-secondary" onclick="downloadPDFDirectly('${url}')">
                                下载PDF文件
                            </button>
                        </div>
                        <p style="font-size: 12px; color: #666; margin-top: 15px;">
                            提示：某些PDF文件可能由于跨域限制无法在iframe中显示
                        </p>
                    </div>
                `);
            }

            // 在新窗口中打开PDF
            function openPDFInNewWindow(url) {
                window.open(url, '_blank');
            }

            // 直接下载PDF
            function downloadPDFDirectly(url) {
                if (url) {
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'document.pdf';
                    a.target = '_blank';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            }

            // 将函数暴露到全局作用域，以便iframe事件可以调用
            window.hideLoading = hideLoading;
            window.showPDFError = showPDFError;
            window.showBlockedPDF = showBlockedPDF;
            // window.tryPDFJSViewer = tryPDFJSViewer;
            window.openPDFInNewWindow = openPDFInNewWindow;
            window.downloadPDFDirectly = downloadPDFDirectly;
            
            // 暴露Canvas宽度控制函数到全局作用域
            window.decreaseCanvasWidth = decreaseCanvasWidth;
            window.increaseCanvasWidth = increaseCanvasWidth;
            window.resetCanvasWidth = resetCanvasWidth;
            window.downloadCurrentPDF = downloadCurrentPDF;

            // 页面卸载时清理资源
            $(window).on('beforeunload', function() {
                if (window.currentBlobUrl) {
                    URL.revokeObjectURL(window.currentBlobUrl);
                }
                if (currentRenderAbortController) {
                    currentRenderAbortController.abort();
                }
                if (renderTimeout) {
                    clearTimeout(renderTimeout);
                }
            });



            // 加载示例PDF
            function loadDemoPDF() {
                showLoading('正在加载示例PDF...');
                
                // 使用项目中的体检报告.pdf作为示例
                const demoUrl = './体检报告.pdf';
                
                // 设置示例PDF的URL
                currentPdfUrl = demoUrl;
                selectedFile = null;
                console.log('设置示例PDF URL:', currentPdfUrl);
                
                // 使用fetch获取PDF数据
                fetch(demoUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('文件不存在');
                        }
                        return response.arrayBuffer();
                    })
                    .then(arrayBuffer => {
                        const typedArray = new Uint8Array(arrayBuffer);
                        loadPDFDocument(typedArray);
                    })
                    .catch(error => {
                        showError('无法加载示例PDF文件：' + error.message);
                    });
            }

            // 拖拽上传功能
            const dropZone = $('.upload-section');
            
            dropZone.on('dragover', function(e) {
                e.preventDefault();
                $(this).css('background-color', '#f0f8ff');
            });

            dropZone.on('dragleave', function(e) {
                e.preventDefault();
                $(this).css('background-color', '');
            });

            dropZone.on('drop', function(e) {
                e.preventDefault();
                $(this).css('background-color', '');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type === 'application/pdf') {
                        $('#pdfFile')[0].files = files;
                        loadPDFFromFile(file);
                    } else {
                        showError('请拖拽有效的PDF文件！');
                    }
                }
            });

            // 键盘快捷键
            $(document).on('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'o':
                            e.preventDefault();
                            $('#pdfFile').click();
                            break;
                    }
                }
            });

            // Canvas宽度控制
            $('#decreaseWidth').on('click', function() {
                decreaseCanvasWidth();
            });

            $('#increaseWidth').on('click', function() {
                increaseCanvasWidth();
            });

            $('#resetWidth').on('click', function() {
                resetCanvasWidth();
            });

            $('#downloadPDF').on('click', function() {
                downloadCurrentPDF();
            });

            // Canvas宽度控制功能
            function decreaseCanvasWidth() {
                if (canvasWidthPercent > 50) { // 最小50%
                    canvasWidthPercent -= 10;
                    updateCanvasWidth();
                    if (pdfDoc) {
                        debouncedRender();
                    }
                    showToast(`Canvas宽度减小到 ${canvasWidthPercent}%`);
                } else {
                    showToast('已达到最小宽度');
                }
            }

            function increaseCanvasWidth() {
                if (canvasWidthPercent < 200) { // 最大200%
                    canvasWidthPercent += 10;
                    updateCanvasWidth();
                    if (pdfDoc) {
                        debouncedRender();
                    }
                    showToast(`Canvas宽度增大到 ${canvasWidthPercent}%`);
                } else {
                    showToast('已达到最大宽度');
                }
            }

            function resetCanvasWidth() {
                canvasWidthPercent = baseCanvasWidth;
                updateCanvasWidth();
                if (pdfDoc) {
                    debouncedRender();
                }
                showToast(`Canvas宽度重置到 ${canvasWidthPercent}%`);
            }

            function updateCanvasWidth() {
                $('#canvasWidth').text(canvasWidthPercent + '%');
                
                // 更新所有已渲染的canvas宽度
                $('.page-placeholder.rendered canvas').css({
                    'width': canvasWidthPercent + '%',
                    'height': 'auto'
                });
            }

            // 下载当前PDF功能
            function downloadCurrentPDF() {
                if (currentPdfUrl) {
                    // 如果有URL，直接下载
                    downloadPDFDirectly(currentPdfUrl);
                    showToast('开始下载PDF文件');
                } else if (selectedFile) {
                    // 如果是本地文件，创建下载链接
                    const url = URL.createObjectURL(selectedFile);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = selectedFile.name;
                    a.target = '_blank';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    showToast('开始下载PDF文件');
                } else {
                    showToast('没有可下载的PDF文件');
                }
            }

            // 触摸手势支持
            let touchStartY = 0;
            let touchStartX = 0;
            let touchStartTime = 0;

            $('#pdfContainer').on('touchstart', function(e) {
                const touch = e.originalEvent.touches[0];
                touchStartY = touch.clientY;
                touchStartX = touch.clientX;
                touchStartTime = Date.now();
            });

            $('#pdfContainer').on('touchend', function(e) {
                const touch = e.originalEvent.changedTouches[0];
                const touchEndY = touch.clientY;
                const touchEndX = touch.clientX;
                const touchEndTime = Date.now();
                
                const deltaY = touchEndY - touchStartY;
                const deltaX = touchEndX - touchStartX;
                const deltaTime = touchEndTime - touchStartTime;
                
                // 检测双击 - 已禁用缩放功能
                if (deltaTime < 300 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
                    console.log('双击功能已禁用');
                }
            });

            // 页面加载时检查URL参数
            checkUrlParameters();
            // extractAndAssignUrlParameters();

            // 初始化提示
            console.log('PDF Canvas 预览器已加载完成！');
            console.log('支持URL参数：?pdf=URL, ?url=URL, ?file=URL, ?src=URL, ?link=URL');
            console.log('示例：https://your-domain.com/pdf.html?pdf=https://example.com/document.pdf');
            console.log('快捷键：Ctrl+O 选择文件');
            console.log('功能：滑动加载，固定缩放，触摸支持，跨域解决方案，URL参数截取赋值');
        });
    </script>

  
</html>